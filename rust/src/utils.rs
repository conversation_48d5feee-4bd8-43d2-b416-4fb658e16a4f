use std::fs;
use std::path::Path;
use anyhow::Result;

pub struct PathUtils;

impl PathUtils {
    pub const FOLDER: &'static str = "/";
    pub const SRC_PATH: &'static str = "/src/main/java/com/client/sdk/";
    pub const IMPORT_SRC: &'static str = "com.client.sdk";
    pub const IMPORT: &'static str = ".";
    pub const IMPORT_MODEL: &'static str = "model";
    pub const IMPORT_API: &'static str = "api";

    pub fn get_folder_model_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        let mut folder_path = Self::get_folder_path(app, is_open, tenant_english_name, env, base_path);
        folder_path.push_str(Self::IMPORT_MODEL);
        folder_path.push_str(Self::FOLDER);
        folder_path
    }

    pub fn get_folder_api_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        let mut folder_path = Self::get_folder_path(app, is_open, tenant_english_name, env, base_path);
        folder_path.push_str(Self::IMPORT_API);
        folder_path.push_str(Self::FOLDER);
        folder_path
    }

    pub fn get_import_model(app: &str, _is_open: bool) -> String {
        let mut import_path = Self::get_import_path(app);
        import_path.push_str(Self::IMPORT_MODEL);
        import_path
    }

    pub fn get_import_api(app: &str, _is_open: bool) -> String {
        let mut import_path = Self::get_import_path(app);
        import_path.push_str(Self::IMPORT_API);
        import_path
    }

    fn get_import_path(app: &str) -> String {
        let mut sb = String::from(Self::IMPORT_SRC);
        sb.push_str(Self::IMPORT);
        sb.push_str(app);
        sb.push_str(Self::IMPORT);
        sb
    }

    pub fn target_source(is_open: bool, env: &str, base_path: &str) -> String {
        let target_source = if is_open {
            format!("{}/open", base_path)
        } else {
            format!("{}/all", base_path)
        };
        format!("{}/{}/", target_source, env)
    }

    /// remove_target_source method - equivalent to Java removeTargetSource()
    pub fn remove_target_source(is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        let mut sb = Self::target_source(is_open, env, base_path);
        sb.push_str(Self::FOLDER);
        sb.push_str(tenant_english_name);
        sb.push_str(Self::SRC_PATH);
        sb
    }

    fn get_folder_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        let mut sb = Self::target_source(is_open, env, base_path);
        if !tenant_english_name.is_empty() {
            sb.push_str(tenant_english_name);
        }
        sb.push_str(Self::SRC_PATH);
        sb.push_str(&app.replace(Self::IMPORT, Self::FOLDER));
        sb.push_str(Self::FOLDER);
        sb
    }

    pub fn java_name(folder_path: &str, class_name: &str) -> String {
        format!("{}{}.java", folder_path, class_name)
    }

    pub fn class_name(name: &str) -> String {
        if name.is_empty() {
            return String::new();
        }

        let mut result = String::new();
        let mut capitalize_next = true;

        for ch in name.chars() {
            if ch.is_alphanumeric() {
                if capitalize_next {
                    result.push(ch.to_uppercase().next().unwrap_or(ch));
                    capitalize_next = false;
                } else {
                    result.push(ch);
                }
            } else {
                capitalize_next = true;
            }
        }

        result
    }
}

pub struct FileUtils;

impl FileUtils {
    pub fn ensure_directory_exists(path: &str) -> Result<()> {
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent)?;
        }
        Ok(())
    }

    pub fn write_to_file(path: &str, content: &str) -> Result<()> {
        Self::ensure_directory_exists(path)?;
        fs::write(path, content)?;
        Ok(())
    }

    pub fn clean_directory(path: &str) -> Result<()> {
        if Path::new(path).exists() {
            fs::remove_dir_all(path)?;
        }
        fs::create_dir_all(path)?;
        Ok(())
    }
}

pub fn is_empty(s: &Option<String>) -> bool {
    match s {
        Some(string) => string.trim().is_empty(),
        None => true,
    }
}
