use crate::models::*;
use anyhow::Result;
use bson::{doc, Document};
use futures::stream::TryStreamExt;
use mongodb::{Client, Collection, Database};
use std::collections::HashMap;

pub struct DatabaseService {
    db: Database,
}

impl DatabaseService {
    pub async fn new(connection_string: &str, database_name: &str) -> Result<Self> {
        let client = Client::with_uri_str(connection_string).await?;
        let db = client.database(database_name);
        Ok(Self { db })
    }

    /// Get applications by tenant - equivalent to Java listApplyByQuery(Map<String, Object> query)
    pub async fn get_all_applications(&self, tenant_id: &str) -> Result<Vec<ApplicationPO>> {
        // Java: Collection = "apply_application_info" (implied from context)
        let collection: Collection<Document> = self.db.collection("apply_application_info");

        // Java: Document document = new Document(); document.put("parentId", "root");
        // Java: List<String> tenantIds = new ArrayList<>(); tenantIds.add(ConstUtil.TENANT_ID);
        // Java: if (!ConstUtil.TENANT_ID.equals(tenantId)) { tenantIds.add(tenantId); }
        // Java: document.put(ConstUtil.TENANT_KEY, new Document("$in", tenantIds));
        let mut tenant_ids = vec!["default".to_string()]; // ConstUtil.TENANT_ID equivalent
        if tenant_id != "default" {
            tenant_ids.push(tenant_id.to_string());
        }

        let filter = doc! {
            "parentId": "root",
            "public_tenant_id": { "$in": tenant_ids }
        };

        let mut cursor = collection.find(filter, None).await?;
        let mut applications = Vec::new();

        while let Some(doc) = cursor.try_next().await? {
            // Java: ApplicationPO applicationPO = JSON.parseObject(JSON.toJSONString(objectMap), ApplicationPO.class);
            if let Ok(app) = bson::from_document::<ApplicationPO>(doc) {
                applications.push(app);
            }
        }

        Ok(applications)
    }

    /// Get models by environment - equivalent to Java modelQuery(String env)
    pub async fn get_models_by_env(&self, env: &str) -> Result<HashMap<String, Vec<StructureDTO>>> {
        // Java: Collection = "apply_datatype_info"
        let collection: Collection<Document> = self.db.collection("apply_datatype_info");

        // Java: Document document = new Document(); document.put("env", env);
        let filter = doc! { "env": env };

        // Java: Map<String, Integer> order = new HashMap<>(); order.put("createTime", -1);
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "createTime": -1 })
            .build();

        let mut cursor = collection.find(filter, options).await?;

        let mut property_map = self.list_all_properties().await?;

        // Java: Map<String, List<StructureDTO>> map = new HashMap<>();
        let mut models_map = HashMap::new();

        while let Some(doc) = cursor.try_next().await? {
            // Java: StructureDTO structure = JSON.parseObject(JSON.toJSONString(objectMap), StructureDTO.class);
            if let Ok(mut structure) = bson::from_document::<StructureDTO>(doc) {
                let app_id = structure.app_id.clone();
                let structure_id = &structure.id;
                property_map.remove(structure_id).map(|properties| {
                    structure.properties = properties;
                });

                // Java: map.put(structure.getAppId(), list);
                models_map.entry(app_id).or_insert_with(Vec::new).push(structure);
            }
        }

        Ok(models_map)
    }


    pub async fn list_all_properties(&self) -> Result<HashMap<String, Vec<PropertyDTO>>> {
        let collection: Collection<Document> = self.db.collection("apply_datatype_param_info");


        let mut cursor = collection.find(None, None).await?;

        let mut property_map = HashMap::new();

        while let Some(doc) = cursor.try_next().await? {
            if let Ok(property) = bson::from_document::<PropertyDTO>(doc) {
                let structure_id = property.structure_id.clone();
                property_map.entry(structure_id).or_insert_with(Vec::new).push(property);
            }
        }

        Ok(property_map)
    }



    /// Get interfaces by environment - equivalent to Java listAll(String env)
    pub async fn get_interfaces_by_env(&self, env: &str) -> Result<HashMap<String, Vec<InterfaceInfoVO>>> {
        // Java: Collection = "apply_interface_info"
        let collection: Collection<Document> = self.db.collection("apply_interface_info");

        // Java: Document document = new Document(); document.put("env", env);
        let filter = doc! { "env": env };

        // Java: No explicit sort in listAll(String env), but other methods use createTime: -1
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "createTime": -1 })
            .build();

        let mut cursor = collection.find(filter, options).await?;

        // Java: Map<String, List<InterfaceInfoVO>> map = new HashMap<>();
        let mut interfaces_map = HashMap::new();

        while let Some(doc) = cursor.try_next().await? {
            // Java: InterfaceInfoVO interfaceInfo = new InterfaceInfoVO();
            // Java: interfaceInfo.setName(objectMap.get("name").toString());
            if let (Some(name), Some(id), Some(alias), Some(app_id), Some(uri), Some(request_method)) = (
                doc.get_str("name").ok(),
                doc.get_object_id("_id").ok(),
                doc.get_str("alias").ok(),
                doc.get_str("appId").ok(),
                doc.get_str("uri").ok(),
                doc.get_str("requestMethod").ok(),
            ) {
                let interface_info = InterfaceInfoVO {
                    id: id.to_hex(),
                    name: name.to_string(),
                    alias: alias.to_string(),
                    app_id: app_id.to_string(),
                    service_name: Some(doc.get_str("serviceName").unwrap_or("").to_string()),
                    path: uri.to_string(),
                    request_method: request_method.to_string(),
                    desc: doc.get_str("desc").ok().map(|s| s.to_string()),
                    request: Vec::new(),
                    response: Vec::new(),
                    tags: None,
                };

                // Java: map.put(interfaceInfo.getAppId(), list);
                interfaces_map.entry(app_id.to_string()).or_insert_with(Vec::new).push(interface_info);
            }
        }

        Ok(interfaces_map)
    }

    /// Get result criteria by tenant - equivalent to Java listResultCriteriaPo(String tenantId)
    pub async fn get_result_criteria(&self, tenant_id: &str) -> Result<HashMap<String, ResultCriteriaPO>> {
        // Java: Collection = "apply_api_result_criteria"
        let collection: Collection<Document> = self.db.collection("apply_api_result_criteria");

        // Java: Document document = new Document(); document.put("public_tenant_id", tenantId);
        let filter = doc! { "public_tenant_id": tenant_id };

        let mut cursor = collection.find(filter, None).await?;

        // Java: Map<String, ResultCriteriaPO> map = new HashMap<>();
        let mut criteria_map = HashMap::new();

        while let Some(doc) = cursor.try_next().await? {
            // Java: ResultCriteriaPO resultCriteriaPO = new ResultCriteriaPO();
            if let (Some(interface_id), Some(id)) = (
                doc.get_str("interface_id").ok(),
                doc.get_object_id("_id").ok(),
            ) {
                let mut criteria = ResultCriteriaPO {
                    id: id.to_hex(),
                    interface_id: interface_id.to_string(),
                    criteria: Vec::new(),
                };

                // Java: Handle criterias array
                if let Ok(criterias_array) = doc.get_array("criterias") {
                    for criteria_value in criterias_array {
                        if let Some(criteria_doc) = criteria_value.as_document() {
                            let criteria_detail = CriteriaDetail {
                                key: criteria_doc.get_str("key").unwrap_or("").to_string(),
                                value: criteria_doc.get_str("value").unwrap_or("").to_string(),
                            };
                            criteria.criteria.push(criteria_detail);
                        }
                    }
                }

                // Java: map.put(resultCriteriaPO.getInterface_id(), resultCriteriaPO);
                criteria_map.insert(interface_id.to_string(), criteria);
            }
        }

        Ok(criteria_map)
    }

    /// Get all parameters - equivalent to Java listParam()
    pub async fn get_all_params(&self) -> Result<HashMap<String, Vec<ParamPO>>> {
        // Java: Collection = "apply_param_info"
        let collection: Collection<Document> = self.db.collection("apply_param_info");

        // Java: QueryCondtion queryCondtion = new QueryCondtion(); Document document = new Document();
        let filter = doc! {};

        // Java: Map<String, Integer> order = new LinkedHashMap<>(); order.put("order", 1); order.put("createTime", -1);
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "order": 1, "createTime": -1 })
            .build();

        let mut cursor = collection.find(filter, options).await?;

        // Java: Map<String, List<ParamPO>> map = new HashMap<>();
        let mut params_map = HashMap::new();

        while let Some(doc) = cursor.try_next().await? {
            // Java: ParamPO paramPO = new ParamPO();
            if let (Some(id), Some(interface_id)) = (
                doc.get_object_id("_id").ok(),
                doc.get_str("interfaceId").ok(),
            ) {
                let param = ParamPO {
                    id: id.to_hex(),
                    interface_id: interface_id.to_string(),
                    name: doc.get_str("name").unwrap_or("").to_string(),
                    param_type: doc.get_str("position").unwrap_or("").to_string(),
                    data_type: doc.get_str("data_type").unwrap_or("String").to_string(),
                    required: doc.get_bool("required").ok(),
                    desc: doc.get_str("desc").ok().map(|s| s.to_string()),
                    example: doc.get_str("defaultValue").ok().map(|v| v.to_string()),
                    children: None,
                };

                // Java: map.put(paramPO.getInterfaceId(), orDefault);
                params_map.entry(interface_id.to_string()).or_insert_with(Vec::new).push(param);
            }
        }

        Ok(params_map)
    }

    /// Get tenant by ID - equivalent to Java tenant query
    pub async fn get_tenant(&self, tenant_id: &str) -> Result<Option<Tenant>> {
        // Java: Collection would be tenant-related collection
        // let collection: Collection<Document> = self.db.collection("tenant_info");

        // // Java: Document filter with _id
        // let filter = doc! { "_id": tenant_id };

        // if let Some(doc) = collection.find_one(filter, None).await? {
        //     // Java: Tenant tenant = JSON.parseObject(JSON.toJSONString(doc), Tenant.class);
        //     if let Ok(tenant) = bson::from_document::<Tenant>(doc) {
        //         return Ok(Some(tenant));
        //     }
        // }
        // Ok(None)
       Ok( Some(Tenant{
            id: "default".to_string(),
            tenant_english_name: "SuperTenant".to_string(),
            name: "超管租户".to_string(),
        }))
        
    }
}
