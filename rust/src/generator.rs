use crate::models::*;
use crate::utils::*;
use anyhow::Result;
use std::collections::{HashMap, HashSet};
use std::io::Write;

/// FastStringWriter equivalent - a string builder for Java code generation
pub struct FastStringWriter {
    buffer: String,
}

impl FastStringWriter {
    pub fn new() -> Self {
        Self {
            buffer: String::new(),
        }
    }

    pub fn append(&mut self, s: &str) {
        self.buffer.push_str(s);
    }

    pub fn to_string(&self) -> String {
        self.buffer.clone()
    }
}

impl Write for FastStringWriter {
    fn write(&mut self, buf: &[u8]) -> std::io::Result<usize> {
        let s = String::from_utf8_lossy(buf);
        self.buffer.push_str(&s);
        Ok(buf.len())
    }

    fn flush(&mut self) -> std::io::Result<()> {
        Ok(())
    }
}

/// StringBufferUtils equivalent for indented code generation
pub struct StringBufferUtils {
    buffer: String,
}

impl StringBufferUtils {
    pub fn new() -> Self {
        Self {
            buffer: String::new(),
        }
    }

    pub fn append(&mut self, s: &str) -> &mut Self {
        self.buffer.push_str(s);
        self
    }

    pub fn append_one(&mut self, s: &str) -> &mut Self {
        self.buffer.push_str("\t");
        self.buffer.push_str(s);
        self
    }

    pub fn append_two(&mut self, s: &str) -> &mut Self {
        self.buffer.push_str("\t\t");
        self.buffer.push_str(s);
        self
    }

    pub fn to_string(&self) -> String {
        self.buffer.clone()
    }
}

/// Main Java code generator following DefaultGenerator.java logic exactly
pub struct DefaultGenerator {
    model: Option<ModelInfo>,
}

impl DefaultGenerator {
    // Constants from Java DefaultGenerator
    const SUFFIX: &'static str = ".java";
    pub const POM: &'static str = "pom.xml";
    const PACKAGE: &'static str = "package ";
    const JAVA_SBUXX: &'static str = ",";
    const CLASS_IMPORT: &'static str = "import ";
    const DATATYPE_LIST: &'static str = "List";
    const DATATYPE_MAP: &'static str = "Map ";
    const DATATYPE_OBJECT: &'static str = "Object";
    const F_NAME_S: &'static str = "«";
    const F_NAME_E: &'static str = "»";
    const F_NAME_CS: &'static str = "<";
    const F_NAME_CE: &'static str = ">";
    const JAVA_END: &'static str = ";";
    const METHOED_PUBLIC: &'static str = "public ";
    const METHOED_STATIC: &'static str = "static ";
    const APICLIENT: &'static str = "ApiClient";
    const METHOED_S: &'static str = "(";
    const METHOED_E: &'static str = ")";
    const METHOED_START: &'static str = "{";
    const METHOED_END: &'static str = "}";
    const IMPORT_ALL: &'static str = ".*";
    const INIT_CLASS: &'static str = "public class %s {";

    pub fn new() -> Self {
        Self { model: None }
    }

    pub fn opts(mut self, model: ModelInfo) -> Self {
        self.model = Some(model);
        self
    }

    /// Main generate method - equivalent to Java generate()
    pub fn generate(&self) -> Result<()> {
        let model = self.model.as_ref().ok_or_else(|| anyhow::anyhow!("Model not set"))?;

        self.clean_dir(model)?;
        // models
        self.generate_models(model)?;
        // apis
        self.generate_apis(model)?;

        Ok(())
    }

    /// Clean directories - equivalent to cleanDir()
    fn clean_dir(&self, model: &ModelInfo) -> Result<()> {
        FileUtils::clean_directory(&model.folder_api_path)?;
        FileUtils::clean_directory(&model.folder_model_path)?;
        Ok(())
    }

    /// Generate models - equivalent to generateModels()
    fn generate_models(&self, model: &ModelInfo) -> Result<()> {
        for structure_dto in &model.models {
            self.model_generate(model, structure_dto)?;
        }
        Ok(())
    }

    /// Generate single model - equivalent to model()
    fn model_generate(&self, model: &ModelInfo, structure_dto: &StructureDTO) -> Result<()> {
        let name = Self::class_name(&structure_dto.name);
        let filename = Self::java_name(&model.folder_model_path, &name);
        let model_context = self.model_context(model, structure_dto)?;
        FileUtils::write_to_file(&filename, &model_context)?;
        Ok(())
    }

    /// Generate Java filename - equivalent to javaName()
    pub fn java_name(folder_path: &str, name: &str) -> String {
        format!("{}{}{}", folder_path, name, Self::SUFFIX)
    }

    /// Generate class name - equivalent to className()
    pub fn class_name(class_name: &str) -> String {
        let input = class_name
            .replace(Self::F_NAME_E, "")
            .replace(Self::F_NAME_S, "")
            .replace(",", "")
            .replace(Self::F_NAME_CE, "")
            .replace(Self::F_NAME_CS, "");

        // For simplicity, we'll skip Chinese character handling for now
        // In a full implementation, you'd add pinyin conversion here

        input
    }

    /// Generate model context - equivalent to modelContext()
    fn model_context(&self, model: &ModelInfo, structure_dto: &StructureDTO) -> Result<String> {
        let mut writer = FastStringWriter::new();

        // Package declaration
        Self::init_page(&mut writer, &model.import_model);

        // Import statements
        Self::import_class(&mut writer, &ModelGenerate::IMPORT_DATE_JSON);
        Self::import_class(&mut writer, &ModelGenerate::IMPORT_JSON_PROPERTY);
        Self::import_class(&mut writer, &ApisGenerate::JAVA_IMPORT_IO);
        Self::import_class(&mut writer, &ApisGenerate::JAVA_IMPORT_UTILS);

        // Class documentation and declaration
        Self::init_author(&mut writer, structure_dto.desc.as_deref().unwrap_or(""));
        Self::init_class(&mut writer, &Self::class_name(&structure_dto.name));

        // Properties
        ModelGenerate::property(&mut writer, &structure_dto.properties);

        // Class end
        Self::class_end(&mut writer);

        Ok(writer.to_string())
    }

    /// Initialize package declaration - equivalent to initPage()
    pub fn init_page(writer: &mut FastStringWriter, name: &str) {
        Self::writer(writer, &format!("{}{}{}", Self::PACKAGE, name, Self::JAVA_END), 1);
    }

    /// Initialize author comment - equivalent to initAuthor()
    pub fn init_author(writer: &mut FastStringWriter, desc: &str) {
        Self::print_line(writer);

        let mut sb = String::new();
        sb.push_str("/**");
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(" *");
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(" * @version 1.0");
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(" * <AUTHOR>
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(" * @注释 ");
        sb.push_str(desc);
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(" */");
        sb.push_str(&ModelGenerate::NEWLINE);

        Self::writer(writer, &sb, 2);
    }

    /// Initialize class declaration - equivalent to initClass()
    pub fn init_class(writer: &mut FastStringWriter, class_name: &str) {
        Self::writer(writer, &format!("public class {} {{", class_name), 2);
    }

    /// End class declaration - equivalent to classEnd()
    pub fn class_end(writer: &mut FastStringWriter) {
        Self::writer(writer, Self::METHOED_END, 0);
    }

    /// Print line - equivalent to prientLine()
    pub fn print_line(writer: &mut FastStringWriter) {
        writer.append("\n");
    }

    /// Write content with newlines - equivalent to writer()
    pub fn writer(writer: &mut FastStringWriter, obj: &str, len: usize) {
        writer.append(obj);
        for _ in 0..len {
            Self::print_line(writer);
        }
    }

    /// Import class - equivalent to importClass()
    pub fn import_class(writer: &mut FastStringWriter, class_name: &str) {
        if !class_name.is_empty() {
            let import_line = format!("{}{}{}", Self::CLASS_IMPORT, class_name, Self::JAVA_END);
            Self::writer(writer, &import_line, 1);
        }
    }

    /// Generate APIs - equivalent to generateApis()
    fn generate_apis(&self, model: &ModelInfo) -> Result<()> {
        let import_model_page = self.import_model_page(model);
        let mut writer = self.init_api(model, &import_model_page);
        let mut method_names = HashSet::new();

        for interface_info in &model.app_api {
            self.api_context(&mut writer, interface_info, &mut method_names, model)?;
        }

        let filename = Self::java_name(&model.folder_api_path, Self::APICLIENT);
        Self::class_end(&mut writer);
        FileUtils::write_to_file(&filename, &writer.to_string())?;

        Ok(())
    }

    /// Get import model page - equivalent to importModelPage()
    fn import_model_page(&self, model: &ModelInfo) -> HashSet<String> {
        let mut items = HashSet::new();
        if !model.models.is_empty() {
            items.insert(format!("{}{}", model.import_model, Self::IMPORT_ALL));
        }
        items
    }

    /// Initialize API writer - equivalent to initApi()
    fn init_api(&self, model: &ModelInfo, import_model_page: &HashSet<String>) -> FastStringWriter {
        let mut writer = FastStringWriter::new();
        Self::init_page(&mut writer, &model.import_api);
        ApisGenerate::import_page(&mut writer, import_model_page);
        Self::init_author(&mut writer, &model.app.name);
        Self::init_class(&mut writer, Self::APICLIENT);
        writer
    }

    /// Generate API context - equivalent to apiContext()
    fn api_context(
        &self,
        writer: &mut FastStringWriter,
        interface_info: &InterfaceInfoVO,
        method_names: &mut HashSet<String>,
        model: &ModelInfo,
    ) -> Result<()> {
        ApisGenerate::method(writer, interface_info, method_names, &model.fication, &model.tenant_id);
        Ok(())
    }
}

/// ModelGenerate equivalent - handles model property generation
pub struct ModelGenerate;

impl ModelGenerate {
    pub const NEWLINE: &'static str = "\n";
    pub const IMPORT_DATE_JSON: &'static str = "com.fasterxml.jackson.annotation.JsonFormat";
    pub const IMPORT_JSON_PROPERTY: &'static str = "com.fasterxml.jackson.annotation.JsonProperty";

    /// Generate properties - equivalent to property()
    pub fn property(writer: &mut FastStringWriter, properties: &[PropertyDTO]) -> HashMap<String, String> {
        let mut pro_map = HashMap::new();
        Self::proper(writer, properties, &mut pro_map);
        Self::proper_get(writer, &pro_map);
        Self::proper_set(writer, &pro_map);
        pro_map
    }

    /// Generate property declarations - equivalent to proper()
    fn proper(writer: &mut FastStringWriter, properties: &[PropertyDTO], pro_map: &mut HashMap<String, String>) {
        for prop in properties {
            DefaultGenerator::writer(writer, &Self::proper_line(prop, pro_map), 2);
        }
    }

    /// Generate property line - equivalent to properLine()
    fn proper_line(prop: &PropertyDTO, pro_map: &mut HashMap<String, String>) -> String {
        let data_type = Self::data_type(&prop.data_type);
        let mut sb = String::new();

        sb.push_str("/**\r\n");
        sb.push_str(&format!(" * {}\n", prop.desc.as_deref().unwrap_or("")));
        sb.push_str(" */\r\n");

        if data_type == "java.util.Date" {
            sb.push_str("@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = \"yyyy-MM-dd HH:mm:ss\")");
            sb.push_str(Self::NEWLINE);
        }

        let field_name = &prop.name;
        let new_field_name = field_name.replace("-", "_");

        pro_map.insert(new_field_name.clone(), data_type.clone());

        sb.push_str(&format!("@JsonProperty(\"{}\")", field_name));
        sb.push_str(Self::NEWLINE);
        sb.push_str(&format!("private {} {}", data_type, new_field_name));
        sb.push_str(DefaultGenerator::JAVA_END);

        sb
    }

    /// Generate getter methods - equivalent to properGet()
    fn proper_get(writer: &mut FastStringWriter, pro_map: &HashMap<String, String>) {
        for (prop, data_type) in pro_map {
            let line_get = Self::line_get(data_type, prop);
            DefaultGenerator::writer(writer, &line_get, 2);
        }
    }

    /// Generate setter methods - equivalent to properSet()
    fn proper_set(writer: &mut FastStringWriter, pro_map: &HashMap<String, String>) {
        for (prop, data_type) in pro_map {
            let line_set = Self::line_set(data_type, prop);
            DefaultGenerator::writer(writer, &line_set, 2);
        }
    }

    /// Generate getter method - equivalent to lineGet()
    fn line_get(data_type: &str, prop: &str) -> String {
        format!(
            "{}{} get{}(){}{}\treturn {};{}{}{}",
            DefaultGenerator::METHOED_PUBLIC,
            data_type,
            Self::pro_name(prop),
            DefaultGenerator::METHOED_START,
            Self::NEWLINE,
            prop,
            DefaultGenerator::JAVA_END,
            Self::NEWLINE,
            DefaultGenerator::METHOED_END
        )
    }

    /// Generate setter method - equivalent to lineSet()
    fn line_set(data_type: &str, prop: &str) -> String {
        let mut result = String::new();
        result.push_str("public void set");
        result.push_str(&Self::pro_name(prop));
        result.push_str("(");
        result.push_str(data_type);
        result.push_str(" ");
        result.push_str(prop);
        result.push_str(")");
        result.push_str(DefaultGenerator::METHOED_START);
        result.push_str(Self::NEWLINE);
        result.push_str("\tthis.");
        result.push_str(prop);
        result.push_str(" = ");
        result.push_str(prop);
        result.push_str(DefaultGenerator::JAVA_END);
        result.push_str(Self::NEWLINE);
        result.push_str(DefaultGenerator::METHOED_END);
        result
    }

    /// Generate property name - equivalent to proName()
    pub fn pro_name(prop: &str) -> String {
        if prop.is_empty() || prop.len() < 2 {
            return if prop.is_empty() { "xxxxx".to_string() } else { prop.to_string() };
        }
        let first_char = prop.chars().next().unwrap().to_uppercase().to_string();
        format!("{}{}", first_char, &prop[1..])
    }

    /// Map data type - simplified version of DefaultGenerator.dataType()
    fn data_type(data_type: &str) -> String {
        match data_type.to_lowercase().as_str() {
            "string" => "String".to_string(),
            "integer" | "int" => "Integer".to_string(),
            "long" => "Long".to_string(),
            "double" => "Double".to_string(),
            "float" => "Float".to_string(),
            "boolean" | "bool" => "Boolean".to_string(),
            "date" | "datetime" => "java.util.Date".to_string(),
            "list" => "List<Object>".to_string(),
            "map" => "Map<String, Object>".to_string(),
            _ => "Object".to_string(),
        }
    }
}

/// ApisGenerate equivalent - handles API method generation
pub struct ApisGenerate;

impl ApisGenerate {
    pub const JAVA_IMPORT_IO: &'static str = "java.io.*";
    pub const JAVA_IMPORT_UTILS: &'static str = "java.util.*";
    const THROWS_EXCEPTION: &'static str = " throws Exception";
    const PLATFORM: &'static str = "platform";
    const AUTHORIZATION: &'static str = "Authorization";

    /// Generate method - equivalent to methoed()
    pub fn method(
        writer: &mut FastStringWriter,
        interface_info: &InterfaceInfoVO,
        method_names: &mut HashSet<String>,
        fication: &str,
        tenant_id: &str,
    ) {
        // Method description
        Self::set_method_desc(writer, interface_info, fication);
        // Method implementation
        Self::set_method(writer, interface_info, method_names, fication, tenant_id);
    }

    /// Set method description - equivalent to setMethodDesc()
    fn set_method_desc(writer: &mut FastStringWriter, interface_info: &InterfaceInfoVO, fication: &str) {
        DefaultGenerator::print_line(writer);

        let mut sb = String::new();
        sb.push_str("/**");
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(&format!(" * @name {}", interface_info.name));
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(&format!(" * @desc {}", interface_info.desc.as_deref().unwrap_or("")));
        sb.push_str(&ModelGenerate::NEWLINE);

        for param in &interface_info.request {
            if Self::is_auth(param, fication) {
                continue;
            }
            sb.push_str(&format!(" * @param {} {}", param.name, param.desc.as_deref().unwrap_or("")));
            sb.push_str(&ModelGenerate::NEWLINE);
        }

        for param in &interface_info.response {
            if param.param_type == "responseBody" {
                sb.push_str(&format!(" * @return {} {}",
                    DefaultGenerator::class_name(&param.data_type),
                    param.desc.as_deref().unwrap_or("")));
                sb.push_str(&ModelGenerate::NEWLINE);
            }
        }

        sb.push_str(" */");
        sb.push_str(&ModelGenerate::NEWLINE);

        DefaultGenerator::writer(writer, &sb, 0);
    }

    /// Set method implementation - equivalent to setMethod()
    fn set_method(
        writer: &mut FastStringWriter,
        interface_info: &InterfaceInfoVO,
        method_names: &mut HashSet<String>,
        fication: &str,
        tenant_id: &str,
    ) {
        let mut sb = String::new();
        sb.push_str(DefaultGenerator::METHOED_PUBLIC);
        sb.push_str(DefaultGenerator::METHOED_STATIC);

        // Return type
        let response_body = Self::response_body(&interface_info.response);
        let method_name = Self::method_name(interface_info, method_names);
        method_names.insert(method_name.clone());

        sb.push_str(&format!("{} {}{}", response_body, method_name, DefaultGenerator::METHOED_S));

        // Method parameters
        sb.push_str(&Self::set_request_param(interface_info, fication));
        sb.push_str(DefaultGenerator::METHOED_E);
        sb.push_str(Self::THROWS_EXCEPTION);
        sb.push_str(DefaultGenerator::METHOED_START);
        sb.push_str(&ModelGenerate::NEWLINE);

        // Method body
        sb.push_str(&Self::set_request_content(interface_info, fication, tenant_id));

        // Method end
        sb.push_str(&ModelGenerate::NEWLINE);
        sb.push_str(DefaultGenerator::METHOED_END);

        DefaultGenerator::writer(writer, &sb, 2);
    }

    /// Import page - equivalent to importPage()
    pub fn import_page(writer: &mut FastStringWriter, import_model_page: &HashSet<String>) {
        let java_import_all = vec![
            Self::JAVA_IMPORT_IO,
            Self::JAVA_IMPORT_UTILS,
            "com.client.api.client.*",
            "com.client.utils.Utils",
            "org.apache.http.util.Asserts",
            "com.client.utils.JsonConvert",
            "com.client.utils.CriteriaUtil",
        ];

        for import in &java_import_all {
            DefaultGenerator::import_class(writer, import);
        }

        for import in import_model_page {
            DefaultGenerator::import_class(writer, import);
        }
    }

    /// Check if parameter is auth - equivalent to isAuth()
    fn is_auth(param: &ParamPO, fication: &str) -> bool {
        fication == Self::PLATFORM
            && param.param_type == "requestHeader"
            && param.name == Self::AUTHORIZATION
    }

    /// Generate method name - equivalent to methodName()
    fn method_name(interface_info: &InterfaceInfoVO, method_names: &HashSet<String>) -> String {
        let mut alias = interface_info.alias.trim().to_string();

        // Handle "Using" suffix
        if let Some(pos) = alias.find("Using") {
            alias = alias[..pos].to_string();
        }

        // Handle path separators
        if alias.contains("/") {
            alias = Self::method_name_from_path(&alias, None);
        }

        Self::is_duplicate(method_names, &alias, 0)
    }

    /// Generate method name from path
    fn method_name_from_path(path: &str, alias: Option<&str>) -> String {
        let parts: Vec<&str> = path.split('/').filter(|s| !s.is_empty()).collect();
        let mut result = String::new();

        for (i, part) in parts.iter().enumerate() {
            if i == 0 {
                result.push_str(part);
            } else {
                result.push_str(&ModelGenerate::pro_name(part));
            }
        }

        if let Some(alias_str) = alias {
            if !result.eq_ignore_ascii_case(alias_str) {
                result.push_str(&ModelGenerate::pro_name(alias_str));
            }
        }

        result
    }

    /// Check for duplicate method names
    fn is_duplicate(method_names: &HashSet<String>, alias: &str, count: usize) -> String {
        let candidate = if count == 0 {
            alias.to_string()
        } else {
            format!("{}{}", alias, count)
        };

        if method_names.contains(&candidate) {
            Self::is_duplicate(method_names, alias, count + 1)
        } else {
            candidate
        }
    }

    /// Set request parameters - equivalent to setRequestParam()
    fn set_request_param(interface_info: &InterfaceInfoVO, fication: &str) -> String {
        let mut sb = String::new();
        let mut key_values = Vec::new();

        for param in &interface_info.request {
            Self::set_header_param(param, &mut key_values, fication);
        }

        Self::set_param(&mut sb, &key_values);
        sb
    }

    /// Set parameter - equivalent to setParam()
    fn set_param(sb: &mut String, key_values: &[(String, String)]) {
        for (i, (key, value)) in key_values.iter().enumerate() {
            let param_type = if key.starts_with("Error-") && key.contains("{") && key.contains("}") {
                DefaultGenerator::DATATYPE_OBJECT
            } else {
                key
            };

            sb.push_str(&format!("{} {}", param_type, value));

            if i < key_values.len() - 1 {
                sb.push_str(DefaultGenerator::JAVA_SBUXX);
            }
        }
    }

    /// Set header parameter - equivalent to setHeaderParam()
    fn set_header_param(param: &ParamPO, key_values: &mut Vec<(String, String)>, fication: &str) {
        if Self::is_auth(param, fication) {
            return;
        }
        Self::set_params(param, key_values);
    }

    /// Set parameters - equivalent to setParams()
    fn set_params(param: &ParamPO, key_values: &mut Vec<(String, String)>) {
        let name = if param.name.is_empty() {
            String::new()
        } else {
            param.name.clone()
        };

        let key = param.data_type.clone();
        let is_query = param.param_type == "requestQuery";
        let value = if is_query {
            name
        } else {
            Self::param_name(&name)
        };

        key_values.push((key, value));
    }

    /// Generate parameter name - equivalent to paramName()
    fn param_name(param: &str) -> String {
        if param.is_empty() {
            return param.to_string();
        }
        let first_char = param.chars().next().unwrap().to_lowercase().to_string();
        format!("{}{}", first_char, &param[1..])
    }

    /// Generate response body type - equivalent to responseBody()
    fn response_body(response: &[ParamPO]) -> String {
        if response.is_empty() {
            return "void".to_string();
        }

        for param in response {
            if param.param_type == "responseBody" {
                return ModelGenerate::data_type(&param.data_type);
            }
        }

        DefaultGenerator::DATATYPE_OBJECT.to_string()
    }

    /// Set request content - equivalent to setRequestContent()
    fn set_request_content(interface_info: &InterfaceInfoVO, fication: &str, tenant_id: &str) -> String {
        let mut sb = StringBufferUtils::new();

        // Required parameter validation
        sb.append(&Self::required_param(interface_info, fication));
        sb.append(&ModelGenerate::NEWLINE);

        // Fixed information
        sb.append(&Self::final_info(interface_info));

        // Path parameter handling
        sb.append(&Self::path_param(interface_info));
        sb.append(&ModelGenerate::NEWLINE);

        // Query parameter handling
        sb.append(&Self::query_param(interface_info));

        // Header parameter handling
        sb.append(&Self::header_param(interface_info, fication, tenant_id));

        // Body parameter handling
        sb.append(&Self::execute(interface_info));

        sb.to_string()
    }

    /// Required parameter validation - equivalent to requiredParam()
    fn required_param(interface_info: &InterfaceInfoVO, fication: &str) -> String {
        let mut sb = StringBufferUtils::new();

        for param in &interface_info.request {
            if param.required.unwrap_or(false) {
                let mut key_values = Vec::new();
                Self::set_header_param(param, &mut key_values, fication);

                if !key_values.is_empty() {
                    let (_, value) = &key_values[0];
                    sb.append_one(&format!("if(Utils.isEmpty({})) {{", value));
                    sb.append(&ModelGenerate::NEWLINE);
                    sb.append_two(&format!("Asserts.notNull({}, \"The {} parameter cannot be empty\");", value, value));
                    sb.append(&ModelGenerate::NEWLINE);
                    sb.append_one("}");
                    sb.append(&ModelGenerate::NEWLINE);
                }
            }
        }

        sb.to_string()
    }

    /// Final info - equivalent to finalInfo()
    fn final_info(interface_info: &InterfaceInfoVO) -> String {
        let mut sb = StringBufferUtils::new();

        // Interface ID
        sb.append_one(&format!("String generator_interfaceId = \"{}\";", interface_info.alias.trim()));
        sb.append(&ModelGenerate::NEWLINE);
        sb.append(&ModelGenerate::NEWLINE);

        // App ID
        sb.append_one(&format!("String generator_appId = \"{}\";", interface_info.app_id.trim()));
        sb.append(&ModelGenerate::NEWLINE);
        sb.append(&ModelGenerate::NEWLINE);

        // Service name
        let service_name = interface_info.service_name.as_deref().unwrap_or("").trim();
        sb.append_one(&format!("String generator_serviceName = \"{}\";", service_name));
        sb.append(&ModelGenerate::NEWLINE);
        sb.append(&ModelGenerate::NEWLINE);

        // Request path
        sb.append_one(&format!("String generator_localVarPath = \"{}\";", interface_info.path.trim()));
        sb.append(&ModelGenerate::NEWLINE);

        sb.to_string()
    }

    /// Path parameter handling - equivalent to pathParam()
    fn path_param(interface_info: &InterfaceInfoVO) -> String {
        let mut sb = StringBufferUtils::new();

        for param in &interface_info.request {
            if param.param_type == "requestPath" {
                let mut key_values = Vec::new();
                Self::set_params(param, &mut key_values);

                if !key_values.is_empty() {
                    let (_, value) = &key_values[0];
                    sb.append_one(&format!(
                        "generator_localVarPath = generator_localVarPath.replaceAll(\"\\\\{{\" + \"{}\" + \"\\\\}}\", HttpUtils.escapeString({}.toString()));",
                        value, value
                    ));
                    sb.append(&ModelGenerate::NEWLINE);
                }
            }
        }

        sb.to_string()
    }

    /// Query parameter handling - equivalent to queryParam()
    fn query_param(interface_info: &InterfaceInfoVO) -> String {
        let mut sb = StringBufferUtils::new();
        let mut has_query = false;

        for param in &interface_info.request {
            if param.param_type == "requestQuery" {
                let name = &param.name;

                if !has_query {
                    sb.append_one("generator_localVarPath = generator_localVarPath + \"?\"");
                    has_query = true;
                } else {
                    sb.append(" + \"&\"");
                }

                sb.append(&format!(" + \"{}=\" + HttpUtils.getEncodedUrl({})", name, name));
            }
        }

        if has_query {
            sb.append(DefaultGenerator::JAVA_END);
            sb.append(&ModelGenerate::NEWLINE);
        }

        sb.to_string()
    }

    /// Header parameter handling - equivalent to headerParam()
    fn header_param(interface_info: &InterfaceInfoVO, fication: &str, tenant_id: &str) -> String {
        let mut sb = StringBufferUtils::new();

        sb.append_one("Map<String, String> generator_headers = new HashMap<String, String>();");
        sb.append(&ModelGenerate::NEWLINE);
        sb.append_one("Optional.ofNullable(com.client.utils.Utils.getRequestValue(\"token\")).ifPresent(it->generator_headers.put(\"token\",it));");
        sb.append(&ModelGenerate::NEWLINE);
        sb.append_one("Optional.ofNullable(com.client.utils.Utils.getRequestValue(\"platform\")).ifPresent(it->generator_headers.put(\"platform\",it));");
        sb.append(&ModelGenerate::NEWLINE);
        sb.append_one(&format!("generator_headers.put(\"tenantId\", \"{}\");", tenant_id));
        sb.append(&ModelGenerate::NEWLINE);

        for param in &interface_info.request {
            if param.param_type == "requestHeader" {
                let name = &param.name;
                let mut key_values = Vec::new();
                Self::set_header_param(param, &mut key_values, fication);

                if !key_values.is_empty() {
                    let (_, value) = &key_values[0];
                    sb.append_one(&format!("generator_headers.put(\"{}\", {});", name, value));
                    sb.append(&ModelGenerate::NEWLINE);
                }
            }
        }

        sb.to_string()
    }

    /// Execute method - equivalent to excute()
    fn execute(interface_info: &InterfaceInfoVO) -> String {
        let mut sb = StringBufferUtils::new();

        sb.append_one(&format!("String generator_methoed = \"{}\";", interface_info.request_method));
        sb.append(&ModelGenerate::NEWLINE);
        sb.append_one("String generator_body = \"\";");
        sb.append(&ModelGenerate::NEWLINE);

        for param in &interface_info.request {
            if param.param_type == "requestBody" {
                let mut key_values = Vec::new();
                Self::set_params(param, &mut key_values);

                if !key_values.is_empty() {
                    let (_, value) = &key_values[0];
                    sb.append_one(&format!("generator_body = JsonConvert.convertToJson({});", value));
                    sb.append(&ModelGenerate::NEWLINE);
                    break;
                }
            }
        }

        sb.append(&ModelGenerate::NEWLINE);
        sb.append(&Self::response_body_execute(interface_info));

        sb.to_string()
    }

    /// Response body execute - equivalent to responseBody() method in ApisGenerate
    fn response_body_execute(interface_info: &InterfaceInfoVO) -> String {
        let mut sb = StringBufferUtils::new();

        sb.append_one("String generator_excute = HttpUtils.excute(generator_localVarPath, generator_methoed, generator_body, generator_headers, generator_appId, generator_serviceName);");
        sb.append(&ModelGenerate::NEWLINE);

        if interface_info.response.is_empty() {
            return sb.to_string();
        }

        for param in &interface_info.response {
            if param.param_type == "responseBody" {
                let class_name = DefaultGenerator::class_name(&param.data_type);
                sb.append_one(&format!("return JsonConvert.json2Obj(generator_excute, {}.class);", class_name));
                sb.append(&ModelGenerate::NEWLINE);
                break;
            }
        }

        sb.to_string()
    }
}