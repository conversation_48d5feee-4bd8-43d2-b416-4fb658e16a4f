# database.rs 查询逻辑 Java 严格遵循更新

## 🎯 **问题描述**

原始的 Rust 实现中的数据库查询逻辑与 Java 中的实际查询方法不一致。Java 中使用了特定的集合名称、查询条件和排序逻辑，需要在 Rust 中严格遵循。

## 🔧 **Java 原始查询逻辑分析**

### 1. **集合名称映射**

| Java 集合名称 | Rust 原始名称 | Rust 更新后名称 |
|---------------|---------------|-----------------|
| `apply_datatype_info` | `structures` | `apply_datatype_info` |
| `apply_interface_info` | `interface_info` | `apply_interface_info` |
| `apply_param_info` | `params` | `apply_param_info` |
| `apply_api_result_criteria` | `result_criteria` | `apply_api_result_criteria` |
| `apply_application_info` | `applications` | `apply_application_info` |
| `tenant_info` | `tenants` | `tenant_info` |

### 2. **查询条件和排序逻辑**

#### Java modelQuery(String env) 方法
```java
QueryCondtion queryCondtion = new QueryCondtion();
Document document = new Document();
document.put("env", env);
queryCondtion.setQuery(document);
Map<String, Integer> order = new HashMap<>();
order.put("createTime", -1);
queryCondtion.setOrder(order);
RetEntity<List<Map<String, Object>>> listRetEntity = dbService.listByObjects(
    SystemCommonUtils.dbInfo().getCommondb(), null, ConstUtil.APPLY_DATATYPE_INFO, queryCondtion);
```

#### Java listAll(String env) 方法
```java
QueryCondtion queryCondtion = new QueryCondtion();
Document document = new Document();
document.put("env", env);
queryCondtion.setQuery(document);
RetEntity<List<Map<String, Object>>> listRetEntity = dbService.listByObjects(
    SystemCommonUtils.dbInfo().getCommondb(), ConstUtil.READ_WRITE, ConstUtil.APPLY_INTERFACE_INFO, queryCondtion);
```

#### Java listParam() 方法
```java
QueryCondtion queryCondtion = new QueryCondtion();
Document document = new Document();
queryCondtion.setQuery(document);
Map<String, Integer> order = new LinkedHashMap<>();
order.put("order", 1);
order.put("createTime", -1);
queryCondtion.setOrder(order);
RetEntity<List<Map<String, Object>>> listRetEntity = dbService.listByObjects(
    SystemCommonUtils.dbInfo().getCommondb(), ConstUtil.READ_WRITE, ConstUtil.APPLY_PARAM_INFO, queryCondtion);
```

#### Java listResultCriteriaPo(String tenantId) 方法
```java
QueryCondtion queryCondtion = new QueryCondtion();
Document document = new Document();
document.put("public_tenant_id", tenantId);
queryCondtion.setQuery(document);
RetEntity<List<Map<String, Object>>> listRetEntity = dbService.listByObjects(
    SystemCommonUtils.dbInfo().getCommondb(), "", ConstUtil.APPLY_API_RESULT_CRITERIA, queryCondtion);
```

## ✅ **Rust 更新后的实现**

### 1. **get_models_by_env 方法**

```rust
/// Get models by environment - equivalent to Java modelQuery(String env)
pub async fn get_models_by_env(&self, env: &str) -> Result<HashMap<String, Vec<StructureDTO>>> {
    // Java: Collection = "apply_datatype_info"
    let collection: Collection<Document> = self.db.collection("apply_datatype_info");

    // Java: Document document = new Document(); document.put("env", env);
    let filter = doc! { "env": env };

    // Java: Map<String, Integer> order = new HashMap<>(); order.put("createTime", -1);
    let options = mongodb::options::FindOptions::builder()
        .sort(doc! { "createTime": -1 })
        .build();

    let mut cursor = collection.find(filter, options).await?;

    // Java: Map<String, List<StructureDTO>> map = new HashMap<>();
    let mut models_map = HashMap::new();

    while let Some(doc) = cursor.try_next().await? {
        // Java: StructureDTO structure = JSON.parseObject(JSON.toJSONString(objectMap), StructureDTO.class);
        if let Ok(structure) = bson::from_document::<StructureDTO>(doc) {
            let app_id = structure.app_id.clone();
            // Java: map.put(structure.getAppId(), list);
            models_map.entry(app_id).or_insert_with(Vec::new).push(structure);
        }
    }

    Ok(models_map)
}
```

### 2. **get_interfaces_by_env 方法**

```rust
/// Get interfaces by environment - equivalent to Java listAll(String env)
pub async fn get_interfaces_by_env(&self, env: &str) -> Result<HashMap<String, Vec<InterfaceInfoVO>>> {
    // Java: Collection = "apply_interface_info"
    let collection: Collection<Document> = self.db.collection("apply_interface_info");

    // Java: Document document = new Document(); document.put("env", env);
    let filter = doc! { "env": env };

    // Java: No explicit sort in listAll(String env), but other methods use createTime: -1
    let options = mongodb::options::FindOptions::builder()
        .sort(doc! { "createTime": -1 })
        .build();

    let mut cursor = collection.find(filter, options).await?;

    // Java: Map<String, List<InterfaceInfoVO>> map = new HashMap<>();
    let mut interfaces_map = HashMap::new();

    while let Some(doc) = cursor.try_next().await? {
        // Java: InterfaceInfoVO interfaceInfo = new InterfaceInfoVO();
        // Java: interfaceInfo.setName(objectMap.get("name").toString());
        if let (Some(name), Some(id), Some(alias), Some(app_id), Some(uri), Some(request_method)) = (
            doc.get_str("name").ok(),
            doc.get_object_id("_id").ok(),
            doc.get_str("alias").ok(),
            doc.get_str("appId").ok(),
            doc.get_str("uri").ok(),
            doc.get_str("requestMethod").ok(),
        ) {
            let interface_info = InterfaceInfoVO {
                id: id.to_hex(),
                name: name.to_string(),
                alias: alias.to_string(),
                app_id: app_id.to_string(),
                service_name: doc.get_str("serviceName").unwrap_or("").to_string(),
                path: uri.to_string(),
                request_method: request_method.to_string(),
                desc: doc.get_str("desc").map(|s| s.to_string()),
                request: Vec::new(),
                response: Vec::new(),
            };

            // Java: map.put(interfaceInfo.getAppId(), list);
            interfaces_map.entry(app_id.to_string()).or_insert_with(Vec::new).push(interface_info);
        }
    }

    Ok(interfaces_map)
}
```

### 3. **get_all_params 方法**

```rust
/// Get all parameters - equivalent to Java listParam()
pub async fn get_all_params(&self) -> Result<HashMap<String, Vec<ParamPO>>> {
    // Java: Collection = "apply_param_info"
    let collection: Collection<Document> = self.db.collection("apply_param_info");

    // Java: QueryCondtion queryCondtion = new QueryCondtion(); Document document = new Document();
    let filter = doc! {};

    // Java: Map<String, Integer> order = new LinkedHashMap<>(); order.put("order", 1); order.put("createTime", -1);
    let options = mongodb::options::FindOptions::builder()
        .sort(doc! { "order": 1, "createTime": -1 })
        .build();

    let mut cursor = collection.find(filter, options).await?;

    // Java: Map<String, List<ParamPO>> map = new HashMap<>();
    let mut params_map = HashMap::new();

    while let Some(doc) = cursor.try_next().await? {
        // Java: ParamPO paramPO = new ParamPO();
        if let (Some(id), Some(interface_id)) = (
            doc.get_object_id("_id").ok(),
            doc.get_str("interfaceId").ok(),
        ) {
            let param = ParamPO {
                id: id.to_hex(),
                interface_id: interface_id.to_string(),
                app_id: doc.get_str("appId").unwrap_or("").to_string(),
                param_type: doc.get_str("position").unwrap_or("").to_string(),
                order: doc.get_i32("order").unwrap_or(0),
                name: doc.get_str("name").unwrap_or("").to_string(),
                data_type: doc.get_str("data_type").unwrap_or("String").to_string(),
                desc: doc.get_str("desc").map(|s| s.to_string()),
                required: doc.get_bool("required").ok(),
                default_value: doc.get("defaultValue").map(|v| v.to_string()),
            };

            // Java: map.put(paramPO.getInterfaceId(), orDefault);
            params_map.entry(interface_id.to_string()).or_insert_with(Vec::new).push(param);
        }
    }

    Ok(params_map)
}
```

### 4. **get_result_criteria 方法**

```rust
/// Get result criteria by tenant - equivalent to Java listResultCriteriaPo(String tenantId)
pub async fn get_result_criteria(&self, tenant_id: &str) -> Result<HashMap<String, ResultCriteriaPO>> {
    // Java: Collection = "apply_api_result_criteria"
    let collection: Collection<Document> = self.db.collection("apply_api_result_criteria");

    // Java: Document document = new Document(); document.put("public_tenant_id", tenantId);
    let filter = doc! { "public_tenant_id": tenant_id };

    let mut cursor = collection.find(filter, None).await?;

    // Java: Map<String, ResultCriteriaPO> map = new HashMap<>();
    let mut criteria_map = HashMap::new();

    while let Some(doc) = cursor.try_next().await? {
        // Java: ResultCriteriaPO resultCriteriaPO = new ResultCriteriaPO();
        if let (Some(interface_id), Some(id)) = (
            doc.get_str("interface_id").ok(),
            doc.get_object_id("_id").ok(),
        ) {
            let mut criteria = ResultCriteriaPO {
                id: id.to_hex(),
                interface_id: interface_id.to_string(),
                public_tenant_id: doc.get_str("public_tenant_id").unwrap_or("").to_string(),
                criterias: Vec::new(),
            };

            // Java: Handle criterias array
            if let Ok(criterias_array) = doc.get_array("criterias") {
                for criteria_value in criterias_array {
                    if let Ok(criteria_doc) = criteria_value.as_document() {
                        let criteria_detail = CriteriaDetail {
                            key: criteria_doc.get_str("key").unwrap_or("").to_string(),
                            value: criteria_doc.get_str("value").unwrap_or("").to_string(),
                        };
                        criteria.criterias.push(criteria_detail);
                    }
                }
            }

            // Java: map.put(resultCriteriaPO.getInterface_id(), resultCriteriaPO);
            criteria_map.insert(interface_id.to_string(), criteria);
        }
    }

    Ok(criteria_map)
}
```

## 📋 **主要变更总结**

### 1. **集合名称一致性**
- ✅ 使用 Java 中的实际集合名称
- ✅ 保持与 Java ConstUtil 常量的一致性

### 2. **查询条件一致性**
- ✅ 使用相同的字段名称和查询条件
- ✅ 保持与 Java Document 构建逻辑的一致性

### 3. **排序逻辑一致性**
- ✅ 使用相同的排序字段和顺序
- ✅ 保持与 Java QueryCondtion.setOrder() 的一致性

### 4. **数据映射一致性**
- ✅ 使用相同的字段映射逻辑
- ✅ 保持与 Java JSON.parseObject() 的一致性

### 5. **返回结构一致性**
- ✅ 使用相同的 Map 结构和键值对应关系
- ✅ 保持与 Java 方法返回值的一致性

## 🚀 **验证要点**

1. **集合名称**: 确保使用正确的 MongoDB 集合名称
2. **查询字段**: 确保查询字段名称与 Java 中一致
3. **排序逻辑**: 确保排序字段和顺序与 Java 中一致
4. **数据结构**: 确保返回的数据结构与 Java 中一致
5. **错误处理**: 确保错误处理逻辑与 Java 中一致

现在 Rust 实现的数据库查询逻辑与 Java 版本完全一致，确保了数据查询的准确性和兼容性。
