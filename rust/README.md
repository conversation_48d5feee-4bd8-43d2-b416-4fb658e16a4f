# SDK3 Generator - Rust Implementation

This is a **strict Rust implementation** of the Java SDK3 functionality that generates Java SDK files from MongoDB interface definitions. The implementation **exactly follows** the logic of `DefaultGenerator.java`, `ModelGenerate.java`, and `ApisGenerate.java` to ensure identical output.

## 🎯 **Strict Java Logic Compliance**

This Rust implementation has been rewritten to **strictly follow** the Java `DefaultGenerator` class logic:

- **DefaultGenerator**: Main generation orchestration
- **ModelGenerate**: Property generation with exact getter/setter formatting
- **ApisGenerate**: API method generation with identical parameter handling
- **FastStringWriter**: Equivalent string building functionality
- **StringBufferUtils**: Indented code generation utilities

## Features

- **MongoDB Integration**: Connects to MongoDB to retrieve interface definitions, models, parameters, and application data
- **Hierarchical Processing**: Handles nested application structures recursively
- **Java Code Generation**: Generates Java model classes and API client classes
- **Package Structure**: Creates proper Java package structures based on application groupings
- **Concurrent Processing**: Uses async/await for efficient parallel processing
- **Template-based Generation**: Uses Handlebars templates for flexible code generation

## Architecture

The implementation consists of several key modules:

- **models.rs**: Data structures representing MongoDB documents (ApplicationPO, InterfaceInfoVO, StructureDTO, etc.)
- **database.rs**: MongoDB connectivity and data retrieval operations
- **generator.rs**: Java code generation using Handlebars templates
- **sdk3.rs**: Main SDK3 logic implementing the recursive generation algorithm
- **utils.rs**: Utility functions for path handling and file operations
- **main.rs**: CLI application entry point

## Installation

1. Ensure you have Rust installed (https://rustup.rs/)
2. Navigate to the rust directory:
   ```bash
   cd rust
   ```
3. Build the project:
   ```bash
   cargo build --release
   ```

## Usage

### Command Line Interface

```bash
cargo run -- \
  --connection-string "mongodb://localhost:27017" \
  --database "api_management" \
  --tenant-id "tenant123" \
  --env "dev" \
  --target-path "/tmp/sdk_output"
```

### Parameters

- `--connection-string, -c`: MongoDB connection string (required)
- `--database, -d`: MongoDB database name (required)
- `--tenant-id, -t`: Tenant ID to generate SDK for (required)
- `--env, -e`: Environment (dev/prod, default: dev)
- `--is-open`: Flag for open tenant (optional)
- `--source-path, -s`: Source template path (default: /tmp/sdk_templates)
- `--target-path`: Target output path (default: /tmp/sdk_generation)

### Example

```bash
# Generate SDK for tenant "acme_corp" in development environment
cargo run -- \
  -c "mongodb://localhost:27017" \
  -d "api_management_db" \
  -t "acme_corp" \
  -e "dev" \
  --target-path "/home/<USER>/generated_sdks"

# Generate SDK for open tenant in production
cargo run -- \
  -c "mongodb://prod-cluster:27017" \
  -d "api_management_prod" \
  -t "open_tenant_123" \
  -e "prod" \
  --is-open \
  --target-path "/var/lib/sdks"
```

## Generated Output

The generator creates a directory structure like:

```
/target-path/
├── tenant_name/
│   └── src/main/java/com/client/sdk/
│       ├── app1/
│       │   ├── model/
│       │   │   ├── UserModel.java
│       │   │   └── ProductModel.java
│       │   └── api/
│       │       └── ApiClient.java
│       └── app2/
│           ├── subapp/
│           │   ├── model/
│           │   └── api/
│           └── ...
```

## MongoDB Collections

The system expects the following MongoDB collections:

- **applications**: Application hierarchy and metadata
- **interface_info**: API interface definitions
- **structures**: Data model structures
- **params**: Interface parameters
- **result_criteria**: Result processing criteria
- **tenants**: Tenant information

## Data Flow

1. **Query Phase**: Retrieve all data from MongoDB collections
2. **Grouping Phase**: Group interfaces and models by application ID
3. **Processing Phase**: Recursively process application hierarchy
4. **Generation Phase**: Generate Java files using templates
5. **Output Phase**: Write files to target directory structure

## Comparison with Java Implementation

This Rust implementation provides equivalent functionality to the Java SDK3 method:

| Java Method | Rust Equivalent | Description |
|-------------|-----------------|-------------|
| `sdk3()` | `generate_sdk3()` | Main entry point |
| `recursionGenerate3()` | `recursion_generate3()` | Recursive processing |
| `generate3()` | `generate3()` | Individual app generation |
| `queryInterface1()` | `query_interface()` | Interface data enhancement |

## Performance Benefits

- **Async Processing**: Non-blocking I/O operations
- **Concurrent Generation**: Parallel processing of applications
- **Memory Efficiency**: Rust's zero-cost abstractions
- **Type Safety**: Compile-time error checking

## Testing

Run the test suite:

```bash
cargo test
```

Run with logging:

```bash
RUST_LOG=info cargo test
```

## Configuration

Environment variables:

- `RUST_LOG`: Set logging level (debug, info, warn, error)
- `MONGODB_URI`: Default MongoDB connection string
- `SDK_TARGET_PATH`: Default target path

## Error Handling

The application uses the `anyhow` crate for comprehensive error handling:

- Database connection errors
- File I/O errors
- Template rendering errors
- Data validation errors

## Dependencies

Key dependencies:

- `mongodb`: MongoDB driver
- `tokio`: Async runtime
- `serde`: Serialization/deserialization
- `handlebars`: Template engine
- `clap`: Command-line argument parsing
- `anyhow`: Error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
