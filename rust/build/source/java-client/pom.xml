<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>io.swagger</groupId>
	<artifactId>java-client</artifactId>
	<packaging>jar</packaging>
	<name>java-client</name>
	<version>${version}</version>
	<description>Swagger Java</description>

	<prerequisites>
		<maven>3.0.0</maven>
	</prerequisites>
	<distributionManagement>
		<repository>
			<id>nexus-snapshot</id>
			<url>http://*************:8081/repository/maven-releases/</url>
		</repository>

		<snapshotRepository>
			<id>nexus-snapshot</id>
			<url>http://*************:8081/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	<build>
		<plugins>
			<!-- <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin> -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.12.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.2.3</version>
				<configuration>
					<skipTests>true</skipTests>    <!--默认关掉单元测试 -->
				</configuration>
			</plugin>
		</plugins>
	</build>


	<dependencies>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.13</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.1.4</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/joda-time/joda-time -->
	<dependency>
	    <groupId>joda-time</groupId>
	    <artifactId>joda-time</artifactId>
	    <version>2.13.0</version>
	</dependency>

	</dependencies>
	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
	</properties>
</project>
