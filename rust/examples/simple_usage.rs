use sdk3_generator::{GenerateConfig, SDK3Generator};
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logger
    env_logger::init();

    println!("SDK3 Generator - Simple Usage Example");
    println!("====================================");

    // Configuration
    let config = GenerateConfig {
        is_open: false,
        source_path: "/tmp/sdk_templates".to_string(),
        target_path: "/tmp/sdk3_example_output".to_string(),
        env: "dev".to_string(),
    };

    // MongoDB connection details
    let connection_string = "mongodb://localhost:27017";
    let database_name = "api_management";
    let tenant_id = "example_tenant";
    let env = "dev";

    println!("Configuration:");
    println!("  MongoDB: {}", connection_string);
    println!("  Database: {}", database_name);
    println!("  Tenant ID: {}", tenant_id);
    println!("  Environment: {}", env);
    println!("  Target Path: {}", config.target_path);
    println!();

    // Create SDK3 Generator
    match SDK3Generator::new(connection_string, database_name, config).await {
        Ok(generator) => {
            println!("✅ SDK3 Generator initialized successfully");
            
            // Generate SDK
            match generator.generate_sdk3(tenant_id, env).await {
                Ok(()) => {
                    println!("✅ SDK3 generation completed successfully!");
                    println!();
                    println!("Generated files should be available at: /tmp/sdk3_example_output");
                    println!("Check the directory structure for generated Java files.");
                }
                Err(e) => {
                    eprintln!("❌ SDK3 generation failed: {}", e);
                    std::process::exit(1);
                }
            }
        }
        Err(e) => {
            eprintln!("❌ Failed to initialize SDK3 Generator: {}", e);
            eprintln!("Make sure MongoDB is running and accessible at {}", connection_string);
            std::process::exit(1);
        }
    }

    Ok(())
}
