[package]
name = "sdk3-generator"
version = "0.1.0"
edition = "2021"

[dependencies]
mongodb = "2.8"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bson = { version = "2.9", features = ["chrono-0_4"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
clap = { version = "4.0", features = ["derive"] }
futures = "0.3"
handlebars = "4.5"
walkdir = "2.4"
toml = "0.8"

[lib]
name = "sdk3_generator"
path = "src/lib.rs"

[[bin]]
name = "sdk3"
path = "src/main.rs"
