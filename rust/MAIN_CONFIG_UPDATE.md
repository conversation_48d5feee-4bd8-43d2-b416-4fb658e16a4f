# main.rs 配置文件处理优化

## 🎯 **变更概述**

优化了 main.rs 中的配置文件处理逻辑，当命令行没有提供配置文件参数时，自动从项目根目录读取 `config.toml` 文件。

## 🔧 **主要变更**

### 1. **命令行参数优化**

**之前**:
```rust
.arg(
    Arg::new("config")
        .short('c')
        .long("config")
        .value_name("CONFIG_FILE")
        .help("Configuration file path")
        .default_value("config.toml"),  // 使用 default_value
)

let config_file = matches.get_one::<String>("config").unwrap();
```

**现在**:
```rust
.arg(
    Arg::new("config")
        .short('c')
        .long("config")
        .value_name("CONFIG_FILE")
        .help("Configuration file path")
        .required(false),  // 设置为非必需参数
)

// 如果命令行没有提供配置文件，使用项目根目录的config.toml
let config_file = matches.get_one::<String>("config")
    .map(|s| s.as_str())
    .unwrap_or("config.toml");
```

### 2. **逻辑改进**

#### 配置文件查找逻辑
1. **优先级**: 命令行参数 > 默认文件路径
2. **默认路径**: 项目根目录的 `config.toml`
3. **灵活性**: 支持相对路径和绝对路径

#### 错误处理保持不变
```rust
let config_content = match fs::read_to_string(config_file) {
    Ok(content) => content,
    Err(e) => {
        error!("Failed to read config file '{}': {}", config_file, e);
        process::exit(1);
    }
};
```

## 🚀 **使用方式**

### 1. **使用默认配置文件**
```bash
# 自动读取项目根目录的 config.toml
./target/release/sdk3

# 或者显式指定（效果相同）
./target/release/sdk3 --config config.toml
```

### 2. **使用自定义配置文件**
```bash
# 使用相对路径
./target/release/sdk3 --config configs/prod.toml

# 使用绝对路径
./target/release/sdk3 --config /etc/sdk3/config.toml

# 使用短参数
./target/release/sdk3 -c my-config.toml
```

### 3. **配置文件结构保持不变**
```toml
# config.toml
connection_string = "mongodb://localhost:27017"
database = "api_management"
tenant_id = "your_tenant_id"

[generate]
is_open = false
source_path = "/tmp/sdk_templates"
base_path = "/tmp/sdk_generation"
env = "dev"
```

## ✅ **优势**

### 1. **用户体验改进**
- ✅ **零配置启动**: 无需任何参数即可运行
- ✅ **约定优于配置**: 默认使用标准位置的配置文件
- ✅ **向后兼容**: 仍支持自定义配置文件路径

### 2. **开发体验改进**
- ✅ **简化命令**: 大多数情况下只需 `./sdk3` 即可运行
- ✅ **标准化**: 遵循常见的配置文件约定
- ✅ **灵活性**: 仍支持多环境配置

### 3. **部署友好**
- ✅ **容器化**: 可以将 config.toml 放在镜像根目录
- ✅ **脚本化**: 自动化脚本无需指定配置文件路径
- ✅ **CI/CD**: 持续集成中更容易使用

## 📋 **使用场景**

### 1. **开发环境**
```bash
# 开发者只需要在项目根目录放置 config.toml
cd /path/to/project
./target/release/sdk3
```

### 2. **测试环境**
```bash
# 使用测试专用配置
./target/release/sdk3 --config configs/test.toml
```

### 3. **生产环境**
```bash
# 使用生产配置
./target/release/sdk3 --config /etc/sdk3/prod.toml

# 或者将生产配置命名为 config.toml 放在运行目录
./target/release/sdk3
```

### 4. **Docker 容器**
```dockerfile
# Dockerfile
COPY config.toml /app/config.toml
WORKDIR /app
CMD ["./sdk3"]
```

## 🔍 **错误处理**

### 1. **配置文件不存在**
```bash
$ ./sdk3
[ERROR sdk3] Failed to read config file 'config.toml': No such file or directory (os error 2)
```

### 2. **配置文件格式错误**
```bash
$ ./sdk3
[ERROR sdk3] Failed to parse config file 'config.toml': invalid TOML value, did you mean to use a quoted string? at line 1 column 1
```

### 3. **自定义配置文件不存在**
```bash
$ ./sdk3 --config missing.toml
[ERROR sdk3] Failed to read config file 'missing.toml': No such file or directory (os error 2)
```

### 4. **配置文件正常读取但业务逻辑错误**
```bash
$ ./sdk3
[ERROR sdk3] SDK3 generation failed: Tenant not found: default
```

## ✅ **测试验证**

### 1. **Help 信息验证**
```bash
$ ./target/release/sdk3 --help
Generates Java SDK files from MongoDB interface definitions

Usage: sdk3 [OPTIONS]

Options:
  -c, --config <CONFIG_FILE>  Configuration file path
  -h, --help                  Print help
  -V, --version               Print version
```

### 2. **默认配置文件测试**
```bash
$ ./target/release/sdk3
# 自动读取 config.toml，如果文件存在且格式正确，会进入业务逻辑
```

### 3. **自定义配置文件测试**
```bash
$ ./target/release/sdk3 --config my-config.toml
# 读取指定的配置文件
```

## 📊 **对比总结**

| 方面 | 之前 | 现在 |
|------|------|------|
| 默认行为 | 需要指定配置文件 | 自动使用 config.toml |
| 命令复杂度 | 中等 | 简单 |
| 用户体验 | 一般 | 优秀 |
| 部署便利性 | 一般 | 优秀 |
| 灵活性 | 高 | 高 |
| 向后兼容 | N/A | 完全兼容 |

## 🎯 **最佳实践**

### 1. **项目结构**
```
project/
├── config.toml          # 默认配置
├── configs/
│   ├── dev.toml        # 开发环境配置
│   ├── test.toml       # 测试环境配置
│   └── prod.toml       # 生产环境配置
└── target/release/sdk3
```

### 2. **配置管理**
- 将敏感信息（如密码）通过环境变量注入
- 使用不同的配置文件管理不同环境
- 在版本控制中包含示例配置文件

### 3. **部署策略**
- 开发环境：使用默认 config.toml
- 测试环境：使用 --config configs/test.toml
- 生产环境：使用 --config /etc/sdk3/prod.toml

现在 SDK3 Generator 提供了更加用户友好的配置文件处理方式，既保持了灵活性，又简化了日常使用。
